{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "ts-node src/app.ts", "dev": "nodemon src/app.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "ts-node src/scripts/seedDatabase.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/joi": "^17.2.2", "@types/jsonwebtoken": "^9.0.8", "@types/lodash": "^4.17.15", "@types/mongoose": "^5.11.96", "@types/node": "^22.13.1", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.3", "nodemon": "^3.1.9", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "dependencies": {"@types/papaparse": "^5.3.15", "@types/pdfkit": "^0.13.9", "@types/xlsx": "^0.0.35", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.10.0", "papaparse": "^5.5.2", "pdfkit": "^0.16.0", "xlsx": "^0.18.5"}}
// server/src/middleware/attendance.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { Types } from 'mongoose';
import { Class } from '../models/class.model';
import { Student } from '../models/student.model';
import { Attendance } from '../models/attendance.model';
import { AppError, DuplicateAttendanceError } from '../types/error.types';
import { AttendanceStatus } from '../types/attendance.types';
import { IUser } from '../types/auth.types';

export class AttendanceMiddleware {
    // Validate that attendance can be marked for the given date
    static async validateAttendanceDate(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { date } = req.body;
            const attendanceDate = new Date(date);
            const today = new Date();

            // Prevent marking attendance for future dates
            if (attendanceDate > today) {
                throw new AppError(400, 'Cannot mark attendance for future dates');
            }

            // Limit how far back attendance can be modified (e.g., 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            if (attendanceDate < thirtyDaysAgo) {
                throw new AppError(400, 'Cannot modify attendance records older than 30 days');
            }

            // Set time to midnight for consistent comparisons
            attendanceDate.setHours(0, 0, 0, 0);
            req.body.date = attendanceDate;

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate class schedule
    static async validateClassSchedule(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { classId } = req.params;
            const { date, isMakeupClass } = req.body;

            // Skip schedule validation for makeup classes
            if (isMakeupClass) {
                return next();
            }

            const classDoc = await Class.findById(classId);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            const attendanceDate = new Date(date);
            const dayOfWeek = attendanceDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

            // Check if class is scheduled for this day
            const hasScheduledClass = classDoc.teachers.some(teacher =>
                teacher.schedule.some(schedule =>
                    schedule.day === dayOfWeek
                )
            );

            if (!hasScheduledClass) {
                throw new AppError(400, 'No class scheduled for this date');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate that attendance hasn't already been marked
    static async validateDuplicateAttendance(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { classId } = req.params;
            const { date, studentId } = req.body;

            // For bulk operations
            const studentIds = req.body.records
                ? req.body.records.map((r: any) => r.studentId)
                : [studentId];

            const existingAttendance = await Attendance.findOne({
                classId: new Types.ObjectId(classId),
                date: new Date(date),
                'students.studentId': {
                    $in: studentIds.map((id: string) => new Types.ObjectId(id))
                }
            });

            if (existingAttendance) {
                throw new DuplicateAttendanceError('Attendance already marked for this date'); 
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate teacher's permission to mark attendance
    static async validateTeacherPermission(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser | undefined;
    
            if (!currentUser?.role || !currentUser?._id) {
                throw new AppError(401, 'User not authenticated');
            }
    
            const { classId } = req.params;
    
            if (currentUser.role === 'superAdmin' || currentUser.role === 'manager') {
                return next();
            }
    
            const classDoc = await Class.findById(classId);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }
    
            // Store _id in a separate variable after validation
            const userId = currentUser._id;  // TypeScript now knows this is defined
    
            const isAssignedTeacher = classDoc.teachers.some(teacher =>
                teacher.teacherId.toString() === userId.toString()
            );
    
            if (!isAssignedTeacher) {
                throw new AppError(403, 'Not authorized to mark attendance for this class');
            }
    
            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate students belong to the class
    static async validateStudentsInClass(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { classId } = req.params;

            // Get student IDs from request
            const studentIds = req.body.records
                ? req.body.records.map((r: any) => r.studentId)
                : [req.body.studentId];

            const students = await Student.find({
                _id: { $in: studentIds.map((id: string) => new Types.ObjectId(id)) },
                currentClass: new Types.ObjectId(classId),
                status: 'active'
            });

            if (students.length !== studentIds.length) {
                throw new AppError(400, 'One or more students not found in class');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate excuse requirements
    static async validateExcuse(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { status } = req.body;

            // Check if excuse is required
            if (status === 'absent' && !req.body.excuse?.reason) {
                throw new AppError(400, 'Excuse reason is required for absent status');
            }

            // Validate excuse format if provided
            if (req.body.excuse) {
                if (req.body.excuse.reason.length < 10) {
                    throw new AppError(400, 'Excuse reason must be at least 10 characters');
                }

                if (req.body.excuse.documentUrl) {
                    try {
                        new URL(req.body.excuse.documentUrl);
                    } catch {
                        throw new AppError(400, 'Invalid document URL format');
                    }
                }
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate late arrival time
    static async validateLateArrival(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const validateSingleRecord = (record: { status: AttendanceStatus; arrivalTime?: Date }) => {
                if (record.status === 'late' && !record.arrivalTime) {
                    throw new AppError(400, 'Arrival time is required for late status');
                }

                if (record.status !== 'late' && record.arrivalTime) {
                    throw new AppError(400, 'Arrival time only applicable for late status');
                }
            };

            if (req.body.records) {
                // Bulk operation
                req.body.records.forEach(validateSingleRecord);
            } else {
                // Single record
                validateSingleRecord(req.body);
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate attendance modification permissions
    static async validateModificationPermission(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { classId } = req.params;
            const { date } = req.body;
            const currentUser = req.user;

            // SuperAdmin and Manager can modify any attendance
            if (currentUser?.role === 'superAdmin' || currentUser?.role === 'manager') {
                return next();
            }

            const attendanceRecord = await Attendance.findOne({
                classId: new Types.ObjectId(classId),
                date: new Date(date)
            });

            if (!attendanceRecord) {
                return next();
            }

            // Teachers can only modify their own attendance records
            if (currentUser?.role === 'teacher') {
                if (!currentUser._id) {
                    throw new AppError(401, 'User not authenticated');
                }

                if (attendanceRecord.teacherId.toString() !== currentUser._id.toString()) {
                    throw new AppError(403, 'Not authorized to modify this attendance record');
                }

                // Teachers can only modify same-day attendance
                const isToday = new Date(date).toDateString() === new Date().toDateString();
                if (!isToday) {
                    throw new AppError(403, 'Teachers can only modify same-day attendance');
                }
            }

            next();
        } catch (error) {
            next(error);
        }
    }
}
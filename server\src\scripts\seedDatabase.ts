// server/src/scripts/seedDatabase.ts
import mongoose from 'mongoose';
import { User } from '../models/user.model';
import { AuthService } from '../services/auth.service';
import { DB_CONFIG } from '../config/database.config';

async function seedSuperAdmin() {
    try {
        // Connect to database
        await mongoose.connect(DB_CONFIG.URI);
        console.log('Connected to database');

        // Check for existing superAdmin
        const existingSuperAdmin = await User.findOne({ role: 'superAdmin' });
        if (existingSuperAdmin) {
            console.log('SuperAdmin already exists');
            await mongoose.disconnect();
            return;
        }

        // Create system ID that will be used as both createdBy and changedBy
        const systemId = new mongoose.Types.ObjectId();

        // Create superAdmin user
        const password = process.env.SUPER_ADMIN_PASSWORD || 'SuperAdmin123!';
        const hashedPassword = await AuthService.hashPassword(password);

        const superAdmin = new User({
            username: 'superadmin',
            password: hashedPassword,
            role: 'superAdmin',
            status: 'active',
            createdBy: systemId,  // Using systemId here
            createdAt: new Date(),
            modifiedAt: new Date(),
            roleHistory: [{
                role: 'superAdmin',
                changedAt: new Date(),
                changedBy: systemId,  // And here
                reason: 'Initial system setup'
            }],
            loginAttempts: []
        });

        // Save the superAdmin user
        await superAdmin.save();
        console.log('SuperAdmin created successfully');

    } catch (error) {
        console.error('Error seeding database:', error);
        if (error instanceof Error) {
            console.error('Error details:', error.message);
        }
    } finally {
        // Always disconnect
        await mongoose.disconnect();
        console.log('Disconnected from database');
    }
}

// Run the seeding function
seedSuperAdmin();
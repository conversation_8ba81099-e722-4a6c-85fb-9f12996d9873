// server/src/services/student.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { Student, IStudentDocument } from '../models/student.model';
import { IStudent, StudentQueryOptions, StudentBulkOperationDTO, StudentUpdateDTO, PaymentRecordDTO, StudentTransferDTO, StudentExportOptions } from '../types/student.types';
import { AppError, ConcurrentOperationError } from '../types/error.types';
import { SystemLogger } from './logger.service';

export class StudentService {
    static async getStudents(options: StudentQueryOptions = {}, requestingUserId: string) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'name',
                sortOrder = 'asc',
                status,
                level,
                search,
                classId,
                fromDate,
                toDate,
                paymentStatus
            } = options;

            const query: FilterQuery<IStudent> = {};

            // Add filters
            if (status) {
                query.status = status;
            }

            if (level) {
                query.currentLevel = level;
            }

            if (classId) {
                query.currentClass = new mongoose.Types.ObjectId(classId);
            }

            if (search) {
                query.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { 'contactInfo.email': { $regex: search, $options: 'i' } },
                    { 'contactInfo.phone': { $regex: search, $options: 'i' } }
                ];
            }

            if (fromDate || toDate) {
                query.registeredAt = {};
                if (fromDate) query.registeredAt.$gte = fromDate;
                if (toDate) query.registeredAt.$lte = toDate;
            }

            if (paymentStatus) {
                const now = new Date();
                switch (paymentStatus) {
                    case 'paid':
                        query['payments.remainingBalance'] = 0;
                        break;
                    case 'pending':
                        query['payments.remainingBalance'] = { $gt: 0 };
                        query['payments.nextDueDate'] = { $gt: now };
                        break;
                    case 'overdue':
                        query['payments.remainingBalance'] = { $gt: 0 };
                        query['payments.nextDueDate'] = { $lt: now };
                        break;
                }
            }

            const skip = (page - 1) * limit;
            const sort: { [key: string]: 'asc' | 'desc' } = {
                [sortBy]: sortOrder
            };

            const [students, total] = await Promise.all([
                Student.find(query)
                    .populate('currentClass', 'name')
                    .sort(sort)
                    .skip(skip)
                    .limit(limit),
                Student.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'list_students',
                performedBy: requestingUserId,
                details: { filters: options },
                status: 'success',
                timestamp: new Date()
            });

            return {
                students,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching students');
        }
    }

    static async getStudentById(id: string, requestingUserId: string) {
        const student = await Student.findById(id)
            .populate('currentClass', 'name')
            .populate('registeredBy', 'username');

        if (!student) {
            throw new AppError(404, 'Student not found');
        }

        await SystemLogger.log({
            severity: 'info',
            category: 'student_management',
            action: 'view_student',
            performedBy: requestingUserId,
            targetId: id,
            details: { studentName: student.name },
            status: 'success',
            timestamp: new Date()
        });

        return student;
    }

    static async createStudent(studentData: Partial<IStudent>, createdBy: string): Promise<IStudentDocument> {
        try {
            // Check if class exists and has capacity (if provided)
            if (studentData.currentClass) {
                const classExists = await mongoose.model('Class').findById(studentData.currentClass);
                if (!classExists) {
                    throw new AppError(404, 'Specified class not found');
                }

                const currentStudents = await Student.countDocuments({
                    currentClass: studentData.currentClass,
                    status: 'active'
                });

                if (currentStudents >= classExists.capacity) {
                    throw new AppError(400, 'Class has reached maximum capacity');
                }

                // Always set the level from the class
                studentData.currentLevel = classExists.level;
            } else if (!studentData.currentLevel) {
                // If no class and no level, we need to reject
                throw new AppError(400, 'Current level is required when no class is assigned');
            }

            const student = new Student({
                ...studentData,
                registeredBy: new mongoose.Types.ObjectId(createdBy),
                registeredAt: new Date(),
                status: 'active'
            });

            await student.save();

            // If class is assigned, add to class history
            if (student.currentClass) {
                student.classHistory.push({
                    classId: student.currentClass,
                    startDate: new Date(),
                    reason: 'Initial Registration'
                });
                await student.save();
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'create_student',
                performedBy: createdBy,
                targetId: student.id,
                details: {
                    studentName: student.name,
                    level: student.currentLevel
                },
                status: 'success',
                timestamp: new Date()
            });

            return student;
        } catch (err: unknown) {
            const error = err as Error;
            console.error('Detailed error:', error);

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error creating student: ${error.message}`);
        }
    }

    static async updateStudent(
        id: string,
        updateData: StudentUpdateDTO,
        updatedBy: string,
        versionCheck?: { version: number }
    ) {
        try {
            const student = await Student.findById(id);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            // Version check
            if (versionCheck && student.__v !== versionCheck.version) {
                throw new ConcurrentOperationError(
                    'This student record has been modified by someone else. Please refresh and try again.',
                    {
                        expectedVersion: versionCheck.version,
                        actualVersion: student.__v
                    }
                );
            }

            // If level is being changed, validate this before updating
            if (updateData.currentLevel && updateData.currentLevel !== student.currentLevel) {
                student.levelHistory.push({
                    fromLevel: student.currentLevel,
                    toLevel: updateData.currentLevel,
                    date: new Date(),
                    reason: 'Administrative Update',
                    approvedBy: new mongoose.Types.ObjectId(updatedBy)
                });
                student.currentLevel = updateData.currentLevel;
            }

            // Update contactInfo properly
            if (updateData.contactInfo) {
                student.contactInfo = {
                    ...student.contactInfo,
                    ...updateData.contactInfo
                };
            }

            // Update name if provided
            if (updateData.name) {
                student.name = updateData.name;
            }

            // Update status if provided, with type check
            if (updateData.status && (updateData.status === 'active' || updateData.status === 'inactive')) {
                student.status = updateData.status;
            }

            // Save the updated student
            await student.save();

            // Log the update
            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'update_student',
                performedBy: updatedBy,
                targetId: student.id,
                details: {
                    studentName: student.name,
                    updates: updateData
                },
                status: 'success',
                timestamp: new Date()
            });

            return student;

        } catch (err: unknown) {
            const error = err as Error;
            console.error('Detailed error:', error);

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error updating student: ${error.message}`);
        }
    }

    static async recordPayment(
        studentId: string,
        paymentData: PaymentRecordDTO,
        recordedBy: string
    ) {
        try {
            console.log('Starting payment recording process', {
                studentId,
                paymentData,
                recordedBy
            });

            const student = await Student.findById(studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            console.log('Current student payments state:', {
                studentId: student.id,
                paymentsCount: student.payments.length,
                lastPayment: student.payments[student.payments.length - 1]
            });

            // Calculate remaining balance
            const currentBalance = student.payments.length > 0
                ? student.payments[student.payments.length - 1].remainingBalance
                : 0;

            console.log('Balance calculation:', {
                currentBalance,
                newPaymentAmount: paymentData.amount
            });

            const remainingBalance = Math.max(0, currentBalance - paymentData.amount);

            // Create the payment record with explicit types
            const payment = {
                amount: Number(paymentData.amount),
                date: new Date(paymentData.date),
                nextDueDate: new Date(paymentData.nextDueDate),
                notes: paymentData.notes,
                recordedBy: new mongoose.Types.ObjectId(recordedBy),
                remainingBalance: Number(remainingBalance)
            };

            console.log('Created payment record:', payment);

            // Add payment to student's payments array
            student.payments.push(payment);

            try {
                console.log('Attempting to save student with new payment');
                await student.save();
                console.log('Student saved successfully with new payment');
            } catch (saveError) {
                console.error('Error saving student:', {
                    error: saveError,
                    validation: (saveError as any).errors,
                    payment: payment
                });
                throw saveError;
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'record_payment',
                performedBy: recordedBy,
                targetId: student.id,
                details: {
                    amount: payment.amount,
                    remainingBalance: payment.remainingBalance
                },
                status: 'success',
                timestamp: new Date()
            });

            return student;

        } catch (err: unknown) {
            console.error('Payment recording error details:', {
                error: err,
                studentId,
                paymentData
            });

            const error = err as Error;

            if (error instanceof mongoose.Error.ValidationError) {
                console.error('Validation error details:', error.errors);
                throw new AppError(400, `Payment validation failed: ${Object.values(error.errors).map(e => e.message).join(', ')}`);
            }

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error recording payment: ${error.message}`);
        }
    }

    static async transferClass(
        studentId: string,
        transferData: StudentTransferDTO,
        transferredBy: string
    ) {
        try {
            console.log('Starting class transfer process:', {
                studentId,
                transferData,
                transferredBy
            });

            const student = await Student.findById(studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            console.log('Found student:', {
                studentId: student.id,
                currentClass: student.currentClass
            });

            // Verify target class exists and has capacity
            const targetClass = await mongoose.model('Class').findById(transferData.toClass);
            if (!targetClass) {
                throw new AppError(404, 'Target class not found');
            }

            console.log('Found target class:', {
                classId: targetClass.id,
                capacity: targetClass.capacity
            });

            const currentStudents = await Student.countDocuments({
                currentClass: transferData.toClass,
                status: 'active'
            });

            console.log('Class capacity check:', {
                currentStudents,
                capacity: targetClass.capacity
            });

            if (currentStudents >= targetClass.capacity) {
                throw new AppError(400, 'Target class has reached maximum capacity');
            }

            // Update student's level if it's different from the class level
            if (student.currentLevel !== targetClass.level) {
                console.log('Updating student level:', {
                    fromLevel: student.currentLevel,
                    toLevel: targetClass.level
                });

                student.levelHistory.push({
                    fromLevel: student.currentLevel,
                    toLevel: targetClass.level,
                    date: new Date(),
                    reason: `Level change due to class transfer: ${transferData.reason}`,
                    approvedBy: new mongoose.Types.ObjectId(transferredBy)
                });
                student.currentLevel = targetClass.level;
            }

            // Handle the transfer
            const targetClassId = new mongoose.Types.ObjectId(transferData.toClass);
            console.log('Preparing transfer:', {
                targetClassId,
                reason: transferData.reason,
                transferDate: transferData.transferDate
            });

            await student.transferClass(
                targetClassId,
                transferData.reason,
                transferData.transferDate ? new Date(transferData.transferDate) : undefined
            );

            // Save the student with the updated class
            await student.save();

            console.log('Transfer completed successfully');

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'transfer_class',
                performedBy: transferredBy,
                targetId: student.id,
                details: {
                    fromClass: student.currentClass,
                    toClass: targetClassId,
                    reason: transferData.reason,
                    transferDate: transferData.transferDate,
                    levelUpdated: student.currentLevel !== targetClass.level
                },
                status: 'success',
                timestamp: new Date()
            });

            return student;

        } catch (err: unknown) {
            console.error('Transfer error details:', {
                error: err,
                studentId,
                transferData
            });

            const error = err as Error;

            if (error instanceof mongoose.Error.ValidationError) {
                console.error('Validation error details:', error.errors);
                throw new AppError(400, `Transfer validation failed: ${Object.values(error.errors).map(e => e.message).join(', ')}`);
            }

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error transferring student: ${error.message}`);
        }
    }

    static async bulkOperation(
        operation: StudentBulkOperationDTO,
        performedBy: string
    ) {
        try {
            const results = await Promise.allSettled(
                operation.studentIds.map(async (studentId) => {
                    const student = await Student.findById(studentId);
                    if (!student) {
                        throw new AppError(404, `Student not found: ${studentId}`);
                    }

                    switch (operation.operation) {
                        case 'activate':
                        case 'deactivate':
                            student.status = operation.operation === 'activate' ? 'active' : 'inactive';
                            break;
                        case 'changeLevel':
                            if (!operation.newValue) {
                                throw new AppError(400, 'New level is required');
                            }
                            student.levelHistory.push({
                                fromLevel: student.currentLevel,
                                toLevel: operation.newValue,
                                date: new Date(),
                                reason: operation.reason,
                                approvedBy: new mongoose.Types.ObjectId(performedBy)
                            });
                            student.currentLevel = operation.newValue;
                            break;
                        case 'assignClass':
                            if (!operation.newValue) {
                                throw new AppError(400, 'Class ID is required');
                            }
                            const targetClass = await mongoose.model('Class')
                                .findById(operation.newValue);

                            if (!targetClass) {
                                throw new AppError(404, 'Target class not found');
                            }

                            await student.transferClass(
                                new mongoose.Types.ObjectId(operation.newValue),
                                operation.reason
                            );
                            break;
                    }

                    return student.save();
                })
            );

            const summary = {
                total: operation.studentIds.length,
                successful: results.filter(r => r.status === 'fulfilled').length,
                failed: results.filter(r => r.status === 'rejected').length,
                errors: results
                    .map((r, i) => r.status === 'rejected' ?
                        { studentId: operation.studentIds[i], error: (r as PromiseRejectedResult).reason.message } :
                        null)
                    .filter(Boolean)
            };

            if (summary.failed === operation.studentIds.length) {
                throw new AppError(400, 'All operations failed');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'bulk_operation',
                performedBy: performedBy,
                details: {
                    operation: operation.operation,
                    summary
                },
                status: 'success',
                timestamp: new Date()
            });

            return summary;
        } catch (error) {
            console.error('Bulk operation error:', error);
            throw error instanceof AppError ? error : new AppError(500, 'Error performing bulk operation');
        }
    }

    static async exportStudents(
        options: StudentExportOptions,
        exportedBy: string
    ): Promise<string> {
        try {
            const query: FilterQuery<IStudent> = {};

            if (options.dateRange) {
                query.registeredAt = {
                    $gte: options.dateRange.start,
                    $lte: options.dateRange.end
                };
            }

            const students = await Student.find(query)
                .populate('currentClass', 'name')
                .sort({ name: 1 });

            const exportData = students.map(student => {
                const baseData = {
                    id: student._id,
                    name: student.name,
                    email: student.contactInfo.email,
                    phone: student.contactInfo.phone,
                    status: student.status,
                    currentLevel: student.currentLevel,
                    className: student.currentClass ? (student.currentClass as any).name : '',
                    registeredAt: student.registeredAt
                };

                if (options.includePaymentHistory) {
                    (baseData as any).payments = student.payments.map(p => ({
                        amount: p.amount,
                        date: p.date,
                        remainingBalance: p.remainingBalance,
                        nextDueDate: p.nextDueDate
                    }));
                }

                if (options.includeClassHistory) {
                    (baseData as any).classHistory = student.classHistory.map(h => ({
                        className: h.classId,
                        startDate: h.startDate,
                        endDate: h.endDate,
                        reason: h.reason
                    }));
                }

                return baseData;
            });

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'export_students',
                performedBy: exportedBy,
                details: {
                    format: options.format,
                    recordCount: exportData.length
                },
                status: 'success',
                timestamp: new Date()
            });

            if (options.format === 'json') {
                return JSON.stringify(exportData, null, 2);
            }

            // CSV format
            const fields = options.fields || Object.keys(exportData[0]);
            const header = fields.join(',');
            const rows = exportData.map(student =>
                fields.map(field => {
                    const value = this.getNestedValue(student, field);
                    return this.formatCSVValue(value);
                }).join(',')
            );

            return [header, ...rows].join('\n');
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting students');
        }
    }

    private static getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }

    static async archiveStudent(studentId: string, archivedBy: string) {
        try {
            const student = await Student.findById(studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            // Archive student data
            const archiveData = await Student.archiveStudent(
                new mongoose.Types.ObjectId(student.id), // Using id instead of _id
                new mongoose.Types.ObjectId(archivedBy)
            );

            // Update student status
            student.status = 'inactive';
            await student.save();

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'archive_student',
                performedBy: archivedBy,
                targetId: student.id, // Using id here as well
                details: {
                    archiveId: archiveData._id
                },
                status: 'success',
                timestamp: new Date()
            });

            return archiveData;
        } catch (error) {
            console.error('Archive error:', error);
            throw error instanceof AppError ? error : new AppError(500, 'Error archiving student');
        }
    }

    static async restoreStudent(studentId: string, restoredBy: string) {
        try {
            // Restore student data
            const restoredStudent = await Student.restoreStudent(
                new mongoose.Types.ObjectId(studentId)
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'student_management',
                action: 'restore_student',
                performedBy: restoredBy,
                targetId: studentId,
                details: {
                    restoredName: restoredStudent.name
                },
                status: 'success',
                timestamp: new Date()
            });

            return restoredStudent;
        } catch (error) {
            console.error('Restore error:', error);
            throw error instanceof AppError ? error : new AppError(500, 'Error restoring student');
        }
    }

    static async checkEmailExists(email: string): Promise<boolean> {
        const student = await Student.findOne({
            'contactInfo.email': email.toLowerCase()
        });
        return !!student;
    }

    static async getPaymentHistory(
        studentId: string,
        options: {
            fromDate?: Date;
            toDate?: Date;
            page: number;
            limit: number;
        },
        requestingUserId: string
    ) {
        const student = await Student.findById(studentId);
        if (!student) {
            throw new AppError(404, 'Student not found');
        }

        let payments = student.payments;

        // Apply date filters if provided
        if (options.fromDate || options.toDate) {
            payments = payments.filter(payment => {
                if (options.fromDate && payment.date < options.fromDate) return false;
                if (options.toDate && payment.date > options.toDate) return false;
                return true;
            });
        }

        // Sort payments by date in descending order
        payments.sort((a, b) => b.date.getTime() - a.date.getTime());

        // Apply pagination
        const start = (options.page - 1) * options.limit;
        const paginatedPayments = payments.slice(start, start + options.limit);

        await SystemLogger.log({
            severity: 'info',
            category: 'student_management',
            action: 'view_payment_history',
            performedBy: requestingUserId,
            targetId: studentId,
            details: { options },
            status: 'success',
            timestamp: new Date()
        });

        return {
            payments: paginatedPayments,
            pagination: {
                total: payments.length,
                page: options.page,
                limit: options.limit,
                pages: Math.ceil(payments.length / options.limit)
            },
            summary: {
                totalPaid: payments.reduce((sum, p) => sum + p.amount, 0),
                latestPayment: payments[0] || null,
                remainingBalance: payments.length > 0 ? payments[0].remainingBalance : 0
            }
        };
    }

    static async getClassHistory(
        studentId: string,
        options: {
            fromDate?: Date;
            toDate?: Date;
            page: number;
            limit: number;
        },
        requestingUserId: string
    ) {
        const student = await Student.findById(studentId);
        if (!student) {
            throw new AppError(404, 'Student not found');
        }

        let classHistory = [...student.classHistory];

        // Apply date filters if provided
        if (options.fromDate || options.toDate) {
            classHistory = classHistory.filter(history => {
                if (options.fromDate && history.startDate < options.fromDate) return false;
                if (options.toDate && history.startDate > options.toDate) return false;
                return true;
            });
        }

        // Sort by start date in descending order
        classHistory.sort((a, b) => b.startDate.getTime() - a.startDate.getTime());

        // Apply pagination
        const start = (options.page - 1) * options.limit;
        const paginatedHistory = classHistory.slice(start, start + options.limit);

        // Populate class details
        const populatedHistory = await Promise.all(
            paginatedHistory.map(async (history) => {
                const classDetails = await mongoose.model('Class').findById(history.classId)
                    .select('name level');

                const historyData = history as unknown as { toObject(): any };
                return {
                    ...historyData.toObject(),
                    classDetails: classDetails || { name: 'Deleted Class', level: 'Unknown' }
                };
            })
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'student_management',
            action: 'view_class_history',
            performedBy: requestingUserId,
            targetId: studentId,
            details: { options },
            status: 'success',
            timestamp: new Date()
        });

        return {
            classHistory: populatedHistory,
            pagination: {
                total: classHistory.length,
                page: options.page,
                limit: options.limit,
                pages: Math.ceil(classHistory.length / options.limit)
            },
            summary: {
                totalClasses: classHistory.length,
                currentClass: student.currentClass ? await mongoose.model('Class')
                    .findById(student.currentClass)
                    .select('name level') : null
            }
        };
    }

    static async identifyLevelMismatches(): Promise<{
        student: any;
        currentClass: any;
        currentLevel: string;
        classLevel: string;
    }[]> {
        // Find students whose level doesn't match their class level
        const students = await Student.find({
            status: 'active',
            currentClass: { $ne: null }
        }).populate({
            path: 'currentClass',
            select: 'name level'
        });

        // Now TypeScript knows this is a populated document
        const mismatches = students.filter(student => {
            // Check if student has a class and it's properly populated
            const populatedClass = student.currentClass as any;
            return populatedClass && student.currentLevel !== populatedClass.level;
        });

        return mismatches.map(student => {
            const populatedClass = student.currentClass as any;
            return {
                student: {
                    id: student._id,
                    name: student.name
                },
                currentClass: {
                    id: populatedClass._id,
                    name: populatedClass.name
                },
                currentLevel: student.currentLevel,
                classLevel: populatedClass.level
            };
        });
    }

    static async getSuggestedClasses(studentId: string): Promise<any[]> {
        const student = await Student.findById(studentId);
        if (!student) {
            throw new AppError(404, 'Student not found');
        }

        // Find active classes that match the student's level
        const suggestedClasses = await mongoose.model('Class').find({
            level: student.currentLevel,
            status: 'active',
            $expr: { $lt: ['$currentStudentCount', '$capacity'] } // Use $expr to compare fields
        }).sort({ currentStudentCount: 1 });

        return suggestedClasses.map(cls => ({
            id: cls._id,
            name: cls.name,
            level: cls.level,
            teacher: cls.teachers.map((t: any) => {
                const teacher = t.teacherId as any;
                return teacher?.username || 'Unknown';
            }).join(', '),
            capacity: {
                total: cls.capacity,
                current: cls.currentStudentCount,
                available: cls.capacity - cls.currentStudentCount
            }
        }));
    }

}